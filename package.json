{"name": "rs-ui-react-components", "version": "0.1.6", "private": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist/**"], "scripts": {"build": "tsup --publicDir src/styles", "cp": "copyfiles src/styles/tailwind.css dist/tailwind.css", "dev": "tsup --treeshake --watch", "lint": "biome lint", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "typecheck": "tsc --noEmit", "check": "biome check", "check:fix": "biome check --fix --unsafe", "changeset": "changeset", "changeset:version": "changeset version", "chromatic": "npx chromatic --project-token=chpt_c980d2db2678865"}, "devDependencies": {"@biomejs/biome": "2.2.4", "@fontsource/public-sans": "^5.2.7", "@remixicon/react": "^4.6.0", "@storybook/addon-a11y": "9.1.8", "@storybook/addon-docs": "^9.1.8", "@storybook/addon-links": "^9.1.8", "@storybook/react-vite": "^9.1.8", "@tailwindcss/postcss": "^4.1.13", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.14", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.4", "@vitest/coverage-v8": "3.2.4", "autoprefixer": "^10.4.21", "chromatic": "^13.2.1", "copyfiles": "^2.4.1", "jsdom": "^27.0.0", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "react": "^19.1.1", "react-dom": "^19.1.1", "storybook": "^9.1.8", "tailwindcss": "^4.1.13", "tsup": "^8.5.0", "typescript": "^5.9.2", "vitest": "^3.2.4", "@changesets/cli": "^2.29.7"}, "repository": {"type": "azure", "url": "https://<EMAIL>/regionskane-utv/sda/_git/rs-ui-react-components"}, "dependencies": {}}