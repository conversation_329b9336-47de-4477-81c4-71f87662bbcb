import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import RsdsLogo from "./RsdsLogo";

describe("RsdsLogo", () => {
	it("renders correctly with default props", () => {
		render(<RsdsLogo />);

		const logo = screen.getByRole("img", { name: "RS Design System Logo" });
		expect(logo).toBeDefined();
	});

	it("renders with custom alt text", () => {
		render(<RsdsLogo alt="Custom logo text" />);

		const logo = screen.getByRole("img", { name: "Custom logo text" });
		expect(logo).toBeDefined();
	});

	it("applies correct variant and size", () => {
		render(<RsdsLogo variant="black-gray" size="lg" />);

		const logo = screen.getByRole("img");
		expect(logo).toBeDefined();
		expect(logo.getAttribute("aria-label")).toBe("RS Design System Logo");
	});

	it("applies custom className", () => {
		render(<RsdsLogo className="custom-class" />);

		const logo = screen.getByRole("img");
		expect(logo).toBeDefined();
		expect(logo.className).toContain("custom-class");
	});
});
