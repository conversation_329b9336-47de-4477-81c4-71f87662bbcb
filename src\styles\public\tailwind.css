@import "tailwindcss";

/* TODO Will be fixed in biome with new verison */
/* biome-ignore lint/suspicious/noUnknownAtRules: Tailwind CSS uses custom at-rules */
@custom-variant dark (&:where(.dark, .dark *));

/* TODO Will be fixed in biome with new verison */
/* biome-ignore lint/suspicious/noUnknownAtRules: Tailwind CSS uses custom at-rules */
@theme static {
	/* Brand */
	--color-cp-cream: rgba(253, 249, 228, 1);
	--color-cp-sea: rgba(48, 124, 142, 1);
	--color-cp-yellow: rgba(253, 211, 47, 1);
	--color-cp-red: rgba(228, 1, 53, 1);
	--color-cp-earth: rgba(95, 82, 54, 1);
	/* Tweaks */
	--color-cp-red--bright: rgba(255, 26, 78, 1);
	--color-cp-sea--dark: rgba(34, 87, 99, 1);
	--color-cp-sea--darker: rgba(2, 58, 71, 1);
	--color-cp-sea--bright: rgba(130, 214, 233, 1);
	--color-cp-sea--brighter: rgba(168, 252, 255, 1);
	--color-cp-sea--brightest: rgba(207, 255, 255, 1);
	--color-cp-saphire: rgba(1, 87, 173, 1);
	--color-cp-saphire--medium: rgba(1, 116, 233, 1);
	--color-cp-saphire--bright: rgba(98, 176, 254, 1);
	--color-cp-saphire--bright-alpha-33: rgba(98, 176, 254, 0.33);
	--color-cp-saphire--bright-alpha-12: rgba(98, 176, 254, 0.12);
	/* Gray scale */
	--color-cp-black: rgba(0, 0, 0, 1);
	--color-cp-black--900: rgba(0, 0, 0, 1);
	--color-cp-grey--800: rgba(32, 32, 32, 1);
	--color-cp-grey--darken-87: rgba(0, 0, 0, 0.87);
	--color-cp-grey--700: rgba(64, 64, 64, 1);
	--color-cp-grey--650: rgba(80, 80, 80, 1);
	--color-cp-grey--600: rgba(96, 96, 96, 1);
	--color-cp-grey--500: rgba(112, 112, 112, 1);
	--color-cp-grey--darken-60: rgba(0, 0, 0, 0.6);
	--color-cp-grey--400: rgba(128, 128, 128, 1);
	--color-cp-grey--300: rgba(160, 160, 160, 1);
	--color-cp-grey--200: rgba(192, 192, 192, 1);
	--color-cp-grey--175: rgba(208, 208, 208, 1);
	--color-cp-grey--150: rgba(224, 224, 224, 1);
	--color-cp-grey--darken-12: rgba(0, 0, 0, 0.12);
	--color-cp-grey--darken-6: rgba(0, 0, 0, 0.06);
	--color-cp-grey--100: rgba(240, 240, 240, 1);
	--color-cp-grey--lighten-38: rgba(255, 255, 255, 0.38);
	--color-cp-grey--lighten-25: rgba(255, 255, 255, 0.25);
	--color-cp-white: rgba(255, 255, 255, 1);
	--color-cp-transparent: rgba(0, 0, 0, 0);

	/* 1177 */
	--color-cp-1177-sky-dark: rgba(59, 66, 102, 1);
	--color-cp-1177-sky-base: rgba(57, 98, 145, 1);
	--color-cp-1177-sky-clear: rgba(0, 151, 227, 1);
	--color-cp-1177-sky-line: rgba(198, 210, 223, 1);
	--color-cp-1177-sky-background: rgba(241, 241, 245, 1);
	--color-cp-1177-grass-dark: rgba(241, 241, 245, 1);
	--color-cp-1177-grass-base: rgba(65, 144, 2, 1);
	--color-cp-1177-grass-clear: rgba(111, 187, 43, 1);
	--color-cp-1177-grass-line: rgba(207, 222, 191, 1);
	--color-cp-1177-grass-background: rgba(238, 248, 238, 1);
	--color-cp-1177-plum-dark: rgba(89, 36, 76, 1);
	--color-cp-1177-plum-base: rgba(244, 115, 159, 1);
	--color-cp-1177-plum-clear: rgba(244, 115, 159, 1);
	--color-cp-1177-plum-line: rgba(245, 201, 216, 1);
	--color-cp-1177-plum-background: rgba(246, 238, 246, 1);
	--color-cp-1177-sun-dark: rgba(246, 238, 246, 1);
	--color-cp-1177-sun-base: rgba(250, 129, 0, 1);
	--color-cp-1177-sun-clear: rgba(255, 193, 0, 1);
	--color-cp-1177-sun-background: rgba(255, 248, 224, 1);
	--color-cp-1177-stone-dark: rgba(53, 53, 53, 1);
	--color-cp-1177-stone-base: rgba(99, 100, 102, 1);
	--color-cp-1177-stone-clear: rgba(128, 130, 133, 1);
	--color-cp-1177-stone-line: rgba(218, 219, 220, 1);
	--color-cp-1177-stone-background: rgba(241, 242, 242, 1);

	/* Color theme ct */
	--color-ct-foreground: var(--color-cp-grey--800);
	--color-ct-foreground--subdued: var(--color-cp-grey--400);
	--color-ct-background: var(--color-cp-white);
	--color-ct-border: var(--color-cp-grey--800);
	--color-ct-link: var(--color-cp-saphire);
	--color-ct-border--selected: var(--color-cp-saphire);
	--color-ct-link--visited: var(--color-cp-saphire--bright);
	--color-ct-bg--selected: var(--color-cp-saphire);
	--color-ct-card-bg--hover: var(--color-cp-saphire--bright-alpha-12);
	--color-ct-card-bg--active: var(--color-cp-saphire--bright-alpha-33);
	--color-ct-focus: var(--color-cp-saphire--medium);
	--color-ct-bg--active: var(--color-cp-grey--darken-12);
	--color-ct-bg--hover: var(--color-cp-grey--darken-6);
	--color-ct-warning: var(--color-cp-red);
	--color-ct-logo-primary: var(--color-cp-red);
	--color-ct-logo-secondar: var(--color-cp-yellow);
	/* menu item */
	--color-ct-menu-item-bg: var(--color-cp-white);
	--color-ct-menu-item-bg--hover: var(--color-cp-grey--100);
	--color-ct-menu-item-outline: var(--color-cp-white);
	--color-ct-menu-item-underline: var(--color-cp-transparent);
	--color-ct-menu-item-underline--hover: var(--color-cp-yellow);
	--color-ct-menu-item-underline--active: var(--color-cp-yellow);

	/* Button */
	--color-ct-button-bg: var(--color-cp-white);
	--color-ct-button-bg--hover: var(--color-cp-grey--100);
	--color-ct-button-bg--active: var(--color-cp-grey--175);
	--color-ct-button-bg--selected: var(--color-cp-saphire--bright-alpha-33);
	--color-ct-main-button-fg: var(--color-cp-cream);
	--color-ct-main-button-fg--disabled: var(--color-cp-grey--darken-60);
	--color-ct-main-button-bg: var(--color-cp-sea);
	--color-ct-main-button-bg--disabled: var(--color-cp-grey--200);
	--color-ct-main-button-bg--hover: var(--color-cp-sea--dark);
	--color-ct-main-button-bg--active: var(--color-cp-sea--darker);
	--color-ct-ghost-button-bg: var(--color-cp-transparent);
	--color-ct-ghost-button-bg--hover: var(--color-cp-grey--darken-6);
	--color-ct-ghost-button-bg--active: var(--color-cp-grey--darken-12);
	--color-ct-ghost-button-bg--selected: var(
		--color-cp-saphire--bright-alpha-33
	);

	/* break points */
	--breakpoint-sm: 19rem;
	--breakpoint-md: 48rem;
	--breakpoint-lg: 80rem;
	--breakpoint-xl: 110.5rem;
	--breakpoint-2xl: 135rem;

	--container-3xs: 16rem;
	--container-2xs: 18rem;
	--container-xs: 20rem;
	--container-sm: 24rem;
	--container-md: 28rem;
	--container-lg: 32rem;
	--container-xl: 36rem;
	--container-2xl: 42rem;
	--container-3xl: 48rem;
	--container-4xl: 56rem;
	--container-5xl: 64rem;
	--container-6xl: 72rem;
	--container-7xl: 80rem;
}

.dark {
	--color-ct-foreground: var(--color-cp-white);
	--color-ct-foreground--subdued: var(--color-cp-grey--300);
	--color-ct-background: var(--color-cp-grey--800);
	--color-ct-border: var(--color-cp-white);
	--color-ct-link: var(--color-cp-saphire--bright);
	--color-ct-border--selected: var(--color-cp-saphire--bright);
	--color-ct-link--visited: var(--color-cp-saphire--bright);
	--color-ct-bg--selected: var(--color-cp-saphire--bright);
	--color-ct-card-bg--hover: var(--color-cp-saphire--bright-alpha-12);
	--color-ct-card-bg--active: var(--color-cp-saphire--bright-alpha-33);
	--color-ct-focus: var(--color-cp-saphire--bright);
	--color-ct-bg--active: var(--color-cp-grey--light-38);
	--color-ct-bg--hover: var(--color-cp-grey--light-25);
	--color-ct-warning: var(--color-cp-red--bright);
	--color-ct-logo-primary: var(--color-cp-white);
	--color-ct-logo-secondar: var(--color-cp-white);

	/* menu item */
	--color-ct-menu-item-bg: var(--color-cp-grey--600);
	--color-ct-menu-item-bg--hover: var(--color-cp-grey--800);
	--color-ct-menu-item-outline: var(--color-cp-white);
	--color-ct-menu-item-underline--hover: var(--color-cp-white);
	--color-ct-menu-item-underline--active: var(--color-cp-white);

	/* Button */
	--color-ct-button-bg: var(--color-cp-grey--800);
	--color-ct-button-bg--hover: var(--color-cp-grey--650);
	--color-ct-button-bg--active: var(--color-cp-grey--500);
	--color-ct-button-bg--selected: var(--color-cp-saphire--bright-alpha-33);
	--color-ct-main-button-fg: var(--color-cp-grey--darken-87);
	--color-ct-main-button-fg--disabled: var(--color-cp-grey--darken-60);
	--color-ct-main-button-bg: var(--color-cp-sea--bright);
	--color-ct-main-button-bg--disabled: var(--color-cp-grey--200);
	--color-ct-main-button-bg--hover: var(--color-cp-sea--brighter);
	--color-ct-main-button-bg--active: var(--color-cp-sea--brightest);
	--color-ct-ghost-button-bg: var(--color-cp-transparent);
	--color-ct-ghost-button-bg--hover: var(--color-cp-grey--25);
	--color-ct-ghost-button-bg--active: var(--color-cp-grey--light-38);
	--color-ct-ghost-button-bg--selected: var(
		--color-cp-saphire--bright-alpha-33
	);
}

:root {
	--background: var(--color-ct-background);
	--foreground: var(--color-ct-foreground);
}

/* TODO Will be fixed in biome with new verison */
/* biome-ignore lint/suspicious/noUnknownAtRules: Tailwind CSS uses custom at-rules */
@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--font-sans: "Public Sans";
	--font-mono: "Public Sans";
}

@media (prefers-color-scheme: dark) {
	:root {
		--background: var(--color-ct-background);
		--foreground: var(--color-ct-foreground);

		--color-ct-foreground: var(--color-cp-white);
		--color-ct-foreground--subdued: var(--color-cp-grey--300);
		--color-ct-background: var(--color-cp-grey--800);
		--color-ct-border: var(--color-cp-white);
		--color-ct-link: var(--color-cp-saphire--bright);
		--color-ct-border--selected: var(--color-cp-saphire--bright);
		--color-ct-link--visited: var(--color-cp-saphire--bright);
		--color-ct-bg--selected: var(--color-cp-saphire--bright);
		--color-ct-card-bg--hover: var(--color-cp-saphire--bright-alpha-12);
		--color-ct-card-bg--active: var(--color-cp-saphire--bright-alpha-33);
		--color-ct-focus: var(--color-cp-saphire--bright);
		--color-ct-bg--active: var(--color-cp-grey--light-38);
		--color-ct-bg--hover: var(--color-cp-grey--light-25);
		--color-ct-warning: var(--color-cp-red--bright);
		--color-ct-logo-primary: var(--color-cp-white);
		--color-ct-logo-secondar: var(--color-cp-white);

		/* menu item */
		--color-ct-menu-item-bg: var(--color-cp-grey--600);
		--color-ct-menu-item-bg--hover: var(--color-cp-grey--800);
		--color-ct-menu-item-outline: var(--color-cp-white);
		--color-ct-menu-item-underline--hover: var(--color-cp-white);
		--color-ct-menu-item-underline--active: var(--color-cp-white);

		/* Button */
		--color-ct-button-bg: var(--color-cp-grey--800);
		--color-ct-button-bg--hover: var(--color-cp-grey--650);
		--color-ct-button-bg--active: var(--color-cp-grey--500);
		--color-ct-button-bg--selected: var(--color-cp-saphire--bright-alpha-33);
		--color-ct-main-button-fg: var(--color-cp-grey--darken-87);
		--color-ct-main-button-fg--disabled: var(--color-cp-grey--darken-60);
		--color-ct-main-button-bg: var(--color-cp-sea--bright);
		--color-ct-main-button-bg--disabled: var(--color-cp-grey--200);
		--color-ct-main-button-bg--hover: var(--color-cp-sea--brighter);
		--color-ct-main-button-bg--active: var(--color-cp-sea--brightest);
		--color-ct-ghost-button-bg: var(--color-cp-transparent);
		--color-ct-ghost-button-bg--hover: var(--color-cp-grey--25);
		--color-ct-ghost-button-bg--active: var(--color-cp-grey--light-38);
		--color-ct-ghost-button-bg--selected: var(
			--color-cp-saphire--bright-alpha-33
		);
	}
}

body {
	background: var(--background);
	color: var(--foreground);
}
